using Dapper;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using System.Data;

namespace ProjectApp.Infrastructure.Repositories
{
    public class InvoiceRepository : IInvoiceRepository
    {
        private readonly IDbConnection _dbConnection;

        public InvoiceRepository(IDbConnection dbConnection)
        {
            _dbConnection = dbConnection;
        }

        public async Task<List<Invoice>> GetAllAsync()
        {
            var sql = "SELECT * FROM Invoices ORDER BY CreatedAt DESC";
            var result = await _dbConnection.QueryAsync<Invoice>(sql);
            return result.ToList();
        }

        public async Task<Invoice> GetByIdAsync(int id)
        {
            var sql = "SELECT * FROM Invoices WHERE Id = @Id";
            return await _dbConnection.QueryFirstOrDefaultAsync<Invoice>(sql, new { Id = id });
        }

        public async Task<Invoice> GetByInvoiceNumberAsync(string invoiceNumber)
        {
            var sql = "SELECT * FROM Invoices WHERE InvoiceNumber = @InvoiceNumber";
            return await _dbConnection.QueryFirstOrDefaultAsync<Invoice>(sql, new { InvoiceNumber = invoiceNumber });
        }

        public async Task<List<Invoice>> GetByCustomerNameAsync(string customerName)
        {
            var sql = "SELECT * FROM Invoices WHERE CustomerName LIKE @CustomerName ORDER BY CreatedAt DESC";
            var result = await _dbConnection.QueryAsync<Invoice>(sql, new { CustomerName = $"%{customerName}%" });
            return result.ToList();
        }

        public async Task<List<Invoice>> GetByStatusAsync(string status)
        {
            var sql = "SELECT * FROM Invoices WHERE Status = @Status ORDER BY CreatedAt DESC";
            var result = await _dbConnection.QueryAsync<Invoice>(sql, new { Status = status });
            return result.ToList();
        }

        public async Task<List<Invoice>> GetByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            var sql = "SELECT * FROM Invoices WHERE InvoiceDate >= @StartDate AND InvoiceDate <= @EndDate ORDER BY InvoiceDate DESC";
            var result = await _dbConnection.QueryAsync<Invoice>(sql, new { StartDate = startDate, EndDate = endDate });
            return result.ToList();
        }

        public async Task<Invoice> CreateAsync(Invoice invoice)
        {
            // Check if invoice number already exists
            if (await InvoiceNumberExistsAsync(invoice.InvoiceNumber))
            {
                throw new Exception($"Invoice number '{invoice.InvoiceNumber}' already exists");
            }

            invoice.CreatedAt = DateTime.UtcNow;
            invoice.UpdatedAt = DateTime.UtcNow;

            var sql = @"INSERT INTO Invoices (InvoiceNumber, CustomerName, CustomerEmail, CustomerAddress, 
                                            InvoiceDate, DueDate, Items, SubTotal, TaxAmount, TotalAmount, 
                                            Currency, Status, Notes, CreatedAt, UpdatedAt, CreatedBy, OriginalPrompt) 
                        OUTPUT INSERTED.Id 
                        VALUES (@InvoiceNumber, @CustomerName, @CustomerEmail, @CustomerAddress, 
                                @InvoiceDate, @DueDate, @Items, @SubTotal, @TaxAmount, @TotalAmount, 
                                @Currency, @Status, @Notes, @CreatedAt, @UpdatedAt, @CreatedBy, @OriginalPrompt)";

            invoice.Id = await _dbConnection.ExecuteScalarAsync<int>(sql, invoice);
            return invoice;
        }

        public async Task<Invoice> UpdateAsync(Invoice invoice)
        {
            invoice.UpdatedAt = DateTime.UtcNow;

            var sql = @"UPDATE Invoices SET 
                        InvoiceNumber = @InvoiceNumber,
                        CustomerName = @CustomerName,
                        CustomerEmail = @CustomerEmail,
                        CustomerAddress = @CustomerAddress,
                        InvoiceDate = @InvoiceDate,
                        DueDate = @DueDate,
                        Items = @Items,
                        SubTotal = @SubTotal,
                        TaxAmount = @TaxAmount,
                        TotalAmount = @TotalAmount,
                        Currency = @Currency,
                        Status = @Status,
                        Notes = @Notes,
                        UpdatedAt = @UpdatedAt
                        WHERE Id = @Id";

            await _dbConnection.ExecuteAsync(sql, invoice);
            return invoice;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var sql = "DELETE FROM Invoices WHERE Id = @Id";
            var rowsAffected = await _dbConnection.ExecuteAsync(sql, new { Id = id });
            return rowsAffected > 0;
        }

        public async Task<bool> InvoiceNumberExistsAsync(string invoiceNumber)
        {
            var sql = "SELECT COUNT(1) FROM Invoices WHERE InvoiceNumber = @InvoiceNumber";
            var count = await _dbConnection.ExecuteScalarAsync<int>(sql, new { InvoiceNumber = invoiceNumber });
            return count > 0;
        }

        public async Task<List<Invoice>> GetRecentInvoicesAsync(int count = 10)
        {
            var sql = "SELECT TOP(@Count) * FROM Invoices ORDER BY CreatedAt DESC";
            var result = await _dbConnection.QueryAsync<Invoice>(sql, new { Count = count });
            return result.ToList();
        }

        public async Task<decimal> GetTotalAmountByStatusAsync(string status)
        {
            var sql = "SELECT ISNULL(SUM(TotalAmount), 0) FROM Invoices WHERE Status = @Status";
            return await _dbConnection.ExecuteScalarAsync<decimal>(sql, new { Status = status });
        }

        public async Task<List<InvoiceItem>> GetInvoiceItemsAsync(int invoiceId)
        {
            var sql = "SELECT * FROM InvoiceItems WHERE InvoiceId = @InvoiceId ORDER BY Id";
            var result = await _dbConnection.QueryAsync<InvoiceItem>(sql, new { InvoiceId = invoiceId });
            return result.ToList();
        }

        public async Task<InvoiceItem> CreateInvoiceItemAsync(InvoiceItem item)
        {
            item.CreatedAt = DateTime.UtcNow;

            var sql = @"INSERT INTO InvoiceItems (InvoiceId, ItemDescription, Quantity, UnitPrice, LineTotal, CreatedAt) 
                        OUTPUT INSERTED.Id 
                        VALUES (@InvoiceId, @ItemDescription, @Quantity, @UnitPrice, @LineTotal, @CreatedAt)";

            item.Id = await _dbConnection.ExecuteScalarAsync<int>(sql, item);
            return item;
        }

        public async Task<bool> DeleteInvoiceItemAsync(int itemId)
        {
            var sql = "DELETE FROM InvoiceItems WHERE Id = @Id";
            var rowsAffected = await _dbConnection.ExecuteAsync(sql, new { Id = itemId });
            return rowsAffected > 0;
        }
    }
}
