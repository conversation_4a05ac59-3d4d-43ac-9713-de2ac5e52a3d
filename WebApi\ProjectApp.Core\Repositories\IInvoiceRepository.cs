using ProjectApp.Core.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ProjectApp.Core.Repositories
{
    public interface IInvoiceRepository
    {
        Task<List<Invoice>> GetAllAsync();
        Task<Invoice> GetByIdAsync(int id);
        Task<Invoice> GetByInvoiceNumberAsync(string invoiceNumber);
        Task<List<Invoice>> GetByCustomerNameAsync(string customerName);
        Task<List<Invoice>> GetByStatusAsync(string status);
        Task<List<Invoice>> GetByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<Invoice> CreateAsync(Invoice invoice);
        Task<Invoice> UpdateAsync(Invoice invoice);
        Task<bool> DeleteAsync(int id);
        Task<bool> InvoiceNumberExistsAsync(string invoiceNumber);
        Task<List<Invoice>> GetRecentInvoicesAsync(int count = 10);
        Task<decimal> GetTotalAmountByStatusAsync(string status);
        Task<List<InvoiceItem>> GetInvoiceItemsAsync(int invoiceId);
        Task<InvoiceItem> CreateInvoiceItemAsync(InvoiceItem item);
        Task<bool> DeleteInvoiceItemAsync(int itemId);
    }
}
