using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ProjectApp.Core.Models
{
    public class Invoice
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string InvoiceNumber { get; set; }
        
        [Required]
        [StringLength(255)]
        public string CustomerName { get; set; }
        
        [StringLength(255)]
        public string CustomerEmail { get; set; }
        
        [StringLength(500)]
        public string CustomerAddress { get; set; }
        
        [Required]
        public DateTime InvoiceDate { get; set; }
        
        public DateTime? DueDate { get; set; }
        
        [Required]
        public string Items { get; set; } // JSON string containing item details
        
        public decimal SubTotal { get; set; }
        
        public decimal TaxAmount { get; set; }
        
        [Required]
        public decimal TotalAmount { get; set; }
        
        [StringLength(10)]
        public string Currency { get; set; } = "USD";
        
        [StringLength(50)]
        public string Status { get; set; } = "Draft";
        
        public string Notes { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        [StringLength(255)]
        public string CreatedBy { get; set; }
        
        public string OriginalPrompt { get; set; } // Store the original user prompt for reference
    }

    public class InvoiceItem
    {
        public int Id { get; set; }
        
        public int InvoiceId { get; set; }
        
        [Required]
        [StringLength(500)]
        public string ItemDescription { get; set; }
        
        public decimal Quantity { get; set; } = 1;
        
        [Required]
        public decimal UnitPrice { get; set; }
        
        [Required]
        public decimal LineTotal { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation property
        public Invoice Invoice { get; set; }
    }

    // DTO for structured invoice data extraction
    public class InvoiceData
    {
        public string InvoiceNumber { get; set; }
        public string CustomerName { get; set; }
        public string CustomerEmail { get; set; }
        public string CustomerAddress { get; set; }
        public DateTime InvoiceDate { get; set; }
        public DateTime? DueDate { get; set; }
        public List<InvoiceItemData> Items { get; set; } = new List<InvoiceItemData>();
        public decimal SubTotal { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public string Currency { get; set; } = "USD";
        public string Notes { get; set; }
    }

    public class InvoiceItemData
    {
        public string Description { get; set; }
        public decimal Quantity { get; set; } = 1;
        public decimal UnitPrice { get; set; }
        public decimal LineTotal { get; set; }
    }
}
