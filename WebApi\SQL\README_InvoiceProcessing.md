# Invoice Processing Plugin

This plugin allows users to create and manage invoices using natural language prompts. The AI agent extracts structured invoice data from user descriptions and saves it to the database.

## Setup Instructions

### 1. Create Database Tables

Run the SQL script to create the required tables:

```sql
-- Run this script in your database
-- File: CreateInvoicesTable.sql
```

This creates:
- `Invoices` table: Main invoice data
- `InvoiceItems` table: Individual line items (optional normalized approach)

### 2. Create Agent Definition

Run the SQL script to create the invoice processing agent:

```sql
-- Run this script in your database  
-- File: CreateInvoiceProcessingAgent.sql
```

This creates the `InvoiceProcessingAgent` that can process invoice prompts.

### 3. Plugin Functions

The `InvoiceProcessingPlugin` provides these functions:

#### `process_invoice_from_prompt`
- **Description**: Processes a user prompt containing invoice details and saves to database
- **Parameter**: `request` (string) - Natural language description of invoice
- **Returns**: Confirmation message with invoice details

#### `get_invoice_by_number`
- **Description**: Retrieves an invoice by its invoice number
- **Parameter**: `invoiceNumber` (string) - Invoice number to search for
- **Returns**: Invoice data in JSON format

#### `get_invoices_by_customer`
- **Description**: Retrieves all invoices for a specific customer
- **Parameter**: `customerName` (string) - Customer name to search for
- **Returns**: List of invoices in JSON format

#### `get_recent_invoices`
- **Description**: Retrieves the most recent invoices
- **Parameter**: `count` (int, optional) - Number of invoices to retrieve (default: 10)
- **Returns**: List of recent invoices in JSON format

## Usage Examples

### Creating Invoices

**Example 1: Basic Invoice**
```
User: "Create an invoice for John Smith for web development services $2500 due next month"
```

**Example 2: Detailed Invoice**
```
User: "Invoice ABC Corp for the following:
- 10 software licenses at $50 each
- Setup fee $200
- Monthly support $100
Invoice date today, due in 30 days"
```

**Example 3: Multiple Items**
```
User: "Create invoice INV-2024-001 for Microsoft:
- Consulting services: 40 hours at $150/hour = $6000
- Travel expenses: $500
- Total: $6500
Customer email: <EMAIL>
Due date: 2024-02-15"
```

### Retrieving Invoices

**Find by Invoice Number:**
```
User: "Find invoice INV-2024-001"
```

**Find by Customer:**
```
User: "Show me all invoices for Microsoft"
```

**Recent Invoices:**
```
User: "Show me the last 5 invoices"
```

## Database Schema

### Invoices Table
- `Id`: Primary key (auto-increment)
- `InvoiceNumber`: Unique invoice identifier
- `CustomerName`: Customer name
- `CustomerEmail`: Customer email (optional)
- `CustomerAddress`: Customer address (optional)
- `InvoiceDate`: Invoice date
- `DueDate`: Payment due date (optional)
- `Items`: JSON string containing line items
- `SubTotal`: Subtotal amount
- `TaxAmount`: Tax amount
- `TotalAmount`: Total amount
- `Currency`: Currency code (default: USD)
- `Status`: Invoice status (Draft, Sent, Paid, Overdue, Cancelled)
- `Notes`: Additional notes
- `CreatedAt`: Creation timestamp
- `UpdatedAt`: Last update timestamp
- `CreatedBy`: Creator identifier
- `OriginalPrompt`: Original user prompt for reference

### InvoiceItems Table (Optional)
- `Id`: Primary key
- `InvoiceId`: Foreign key to Invoices table
- `ItemDescription`: Item description
- `Quantity`: Item quantity
- `UnitPrice`: Price per unit
- `LineTotal`: Line total amount
- `CreatedAt`: Creation timestamp

## AI Processing

The plugin uses AI to extract structured data from natural language prompts:

1. **Prompt Analysis**: AI analyzes the user's natural language description
2. **Data Extraction**: Extracts invoice fields like customer info, items, amounts, dates
3. **Validation**: Validates and calculates totals, taxes, etc.
4. **Database Storage**: Saves structured data to the database
5. **Confirmation**: Returns confirmation with created invoice details

## Error Handling

The plugin handles various error scenarios:
- Invalid or incomplete prompts
- Duplicate invoice numbers
- Database connection issues
- AI processing failures

## Integration

The plugin integrates with:
- **Semantic Kernel**: For AI function calling
- **Agent System**: As a tool for AI agents
- **Database**: Using Dapper ORM with SQL Server
- **Logging**: Agent activity logging for audit trails

## Testing

Test the plugin with various invoice scenarios:
1. Simple single-item invoices
2. Multi-item invoices with calculations
3. Invoices with customer details
4. Invoices with dates and due dates
5. Retrieval operations

The AI agent will automatically extract relevant information and create properly structured invoices in the database.
