-- Create Invoices table for storing invoice data extracted from user prompts
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Invoices' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Invoices] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [InvoiceNumber] NVARCHAR(100) NOT NULL,
        [CustomerName] NVARCHAR(255) NOT NULL,
        [CustomerEmail] NVARCHAR(255) NULL,
        [CustomerAddress] NVARCHAR(500) NULL,
        [InvoiceDate] DATETIME NOT NULL,
        [DueDate] DATETIME NULL,
        [Items] NVARCHAR(MAX) NOT NULL, -- JSON string containing item details
        [SubTotal] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [TaxAmount] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [TotalAmount] DECIMAL(18,2) NOT NULL,
        [Currency] NVARCHAR(10) NOT NULL DEFAULT 'USD',
        [Status] NVARCHAR(50) NOT NULL DEFAULT 'Draft', -- Draft, Sent, Paid, Overdue, Cancelled
        [Notes] NVARCHAR(MAX) NULL,
        [CreatedAt] DATETIME NOT NULL DEFAULT GETUTCDATE(),
        [UpdatedAt] DATETIME NOT NULL DEFAULT GETUTCDATE(),
        [CreatedBy] NVARCHAR(255) NULL,
        [OriginalPrompt] NVARCHAR(MAX) NULL -- Store the original user prompt for reference
    );
    
    -- Create indexes for better performance
    CREATE INDEX IX_Invoices_InvoiceNumber ON [dbo].[Invoices] ([InvoiceNumber]);
    CREATE INDEX IX_Invoices_CustomerName ON [dbo].[Invoices] ([CustomerName]);
    CREATE INDEX IX_Invoices_InvoiceDate ON [dbo].[Invoices] ([InvoiceDate]);
    CREATE INDEX IX_Invoices_Status ON [dbo].[Invoices] ([Status]);
    CREATE INDEX IX_Invoices_CreatedAt ON [dbo].[Invoices] ([CreatedAt]);
    
    PRINT 'Invoices table created successfully';
END
ELSE
BEGIN
    PRINT 'Invoices table already exists';
END

-- Create InvoiceItems table for storing individual line items (optional normalized approach)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='InvoiceItems' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[InvoiceItems] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [InvoiceId] INT NOT NULL,
        [ItemDescription] NVARCHAR(500) NOT NULL,
        [Quantity] DECIMAL(18,4) NOT NULL DEFAULT 1,
        [UnitPrice] DECIMAL(18,2) NOT NULL,
        [LineTotal] DECIMAL(18,2) NOT NULL,
        [CreatedAt] DATETIME NOT NULL DEFAULT GETUTCDATE(),
        
        CONSTRAINT FK_InvoiceItems_Invoice FOREIGN KEY ([InvoiceId]) REFERENCES [dbo].[Invoices]([Id]) ON DELETE CASCADE
    );
    
    CREATE INDEX IX_InvoiceItems_InvoiceId ON [dbo].[InvoiceItems] ([InvoiceId]);
    
    PRINT 'InvoiceItems table created successfully';
END
ELSE
BEGIN
    PRINT 'InvoiceItems table already exists';
END
