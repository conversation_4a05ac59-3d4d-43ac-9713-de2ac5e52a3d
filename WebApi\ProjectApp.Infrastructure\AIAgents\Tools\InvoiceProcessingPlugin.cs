using Microsoft.SemanticKernel;
using System.ComponentModel;
using System.Text.Json;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using Microsoft.SemanticKernel.ChatCompletion;

namespace ProjectApp.Infrastructure.AIAgents.Tools
{
    public class InvoiceProcessingPlugin
    {
        private readonly IInvoiceRepository _invoiceRepository;
        private readonly IAgentLogRepository _agentLogRepository;
        private readonly IChatCompletionService _chatCompletionService;

        public InvoiceProcessingPlugin(
            IInvoiceRepository invoiceRepository, 
            IAgentLogRepository agentLogRepository,
            IChatCompletionService chatCompletionService)
        {
            _invoiceRepository = invoiceRepository;
            _agentLogRepository = agentLogRepository;
            _chatCompletionService = chatCompletionService;
        }

        [KernelFunction("process_invoice_from_prompt")]
        [Description("Processes a user prompt containing invoice details and extracts structured data to save in the database. The prompt should contain invoice information like customer details, items, amounts, dates, etc.")]
        public async Task<string> ProcessInvoiceFromPromptAsync(
            [Description("User prompt containing invoice details in natural language")] string request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request))
                {
                    return "Error: Invoice request cannot be empty.";
                }

                // Log the request
                var log = new AgentLog
                {
                    AgentName = "InvoiceProcessingAgent",
                    FunctionName = "process_invoice_from_prompt",
                    Parameters = JsonSerializer.Serialize(new { request }),
                    Status = "Processing",
                    CreatedAt = DateTime.UtcNow
                };
                await _agentLogRepository.CreateLog(log);

                // Use AI to extract structured invoice data from the prompt
                var extractedData = await ExtractInvoiceDataFromPrompt(request);
                
                if (extractedData == null)
                {
                    return "Error: Could not extract valid invoice data from the provided prompt.";
                }

                // Convert to Invoice entity
                var invoice = new Invoice
                {
                    InvoiceNumber = extractedData.InvoiceNumber ?? GenerateInvoiceNumber(),
                    CustomerName = extractedData.CustomerName ?? "Unknown Customer",
                    CustomerEmail = extractedData.CustomerEmail,
                    CustomerAddress = extractedData.CustomerAddress,
                    InvoiceDate = extractedData.InvoiceDate != default ? extractedData.InvoiceDate : DateTime.Now,
                    DueDate = extractedData.DueDate,
                    Items = JsonSerializer.Serialize(extractedData.Items),
                    SubTotal = extractedData.SubTotal,
                    TaxAmount = extractedData.TaxAmount,
                    TotalAmount = extractedData.TotalAmount,
                    Currency = extractedData.Currency,
                    Status = "Draft",
                    Notes = extractedData.Notes,
                    CreatedBy = "AI Agent",
                    OriginalPrompt = request
                };

                // Save to database
                var savedInvoice = await _invoiceRepository.CreateAsync(invoice);

                // Update log status
                log.Status = "Completed";
                log.Parameters = JsonSerializer.Serialize(new { request, invoiceId = savedInvoice.Id });

                return $"Invoice successfully created with ID: {savedInvoice.Id}, Invoice Number: {savedInvoice.InvoiceNumber}, Customer: {savedInvoice.CustomerName}, Total Amount: {savedInvoice.Currency} {savedInvoice.TotalAmount:F2}";
            }
            catch (Exception ex)
            {
                return $"Error processing invoice: {ex.Message}";
            }
        }

        private async Task<InvoiceData> ExtractInvoiceDataFromPrompt(string prompt)
        {
            try
            {
                var extractionPrompt = $@"
Extract invoice information from the following user prompt and return it as a JSON object with this exact structure:
{{
    ""InvoiceNumber"": ""string or null"",
    ""CustomerName"": ""string"",
    ""CustomerEmail"": ""string or null"",
    ""CustomerAddress"": ""string or null"",
    ""InvoiceDate"": ""2024-01-01T00:00:00"",
    ""DueDate"": ""2024-01-01T00:00:00 or null"",
    ""Items"": [
        {{
            ""Description"": ""string"",
            ""Quantity"": 1.0,
            ""UnitPrice"": 0.0,
            ""LineTotal"": 0.0
        }}
    ],
    ""SubTotal"": 0.0,
    ""TaxAmount"": 0.0,
    ""TotalAmount"": 0.0,
    ""Currency"": ""USD"",
    ""Notes"": ""string or null""
}}

Rules:
1. Extract all available information from the prompt
2. If invoice number is not provided, set to null
3. Calculate LineTotal as Quantity * UnitPrice for each item
4. Calculate SubTotal as sum of all LineTotals
5. If tax is mentioned, extract TaxAmount, otherwise set to 0
6. Calculate TotalAmount as SubTotal + TaxAmount
7. Use ISO date format for dates
8. If dates are not specified, use current date for InvoiceDate
9. Return only valid JSON, no additional text

User prompt: {prompt}";

                var result = await _chatCompletionService.GetChatMessageContentAsync(extractionPrompt);
                var jsonResponse = result.Content?.Trim();

                if (string.IsNullOrWhiteSpace(jsonResponse))
                {
                    return null;
                }

                // Clean up the JSON response (remove any markdown formatting)
                if (jsonResponse.StartsWith("```json"))
                {
                    jsonResponse = jsonResponse.Substring(7);
                }
                if (jsonResponse.EndsWith("```"))
                {
                    jsonResponse = jsonResponse.Substring(0, jsonResponse.Length - 3);
                }

                var invoiceData = JsonSerializer.Deserialize<InvoiceData>(jsonResponse, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                return invoiceData;
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to extract invoice data: {ex.Message}");
            }
        }

        private string GenerateInvoiceNumber()
        {
            var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
            var random = new Random().Next(1000, 9999);
            return $"INV-{timestamp}-{random}";
        }

        [KernelFunction("get_invoice_by_number")]
        [Description("Retrieves an invoice by its invoice number")]
        public async Task<string> GetInvoiceByNumberAsync(
            [Description("Invoice number to search for")] string invoiceNumber)
        {
            try
            {
                var invoice = await _invoiceRepository.GetByInvoiceNumberAsync(invoiceNumber);
                if (invoice == null)
                {
                    return $"Invoice with number '{invoiceNumber}' not found.";
                }

                return JsonSerializer.Serialize(invoice, new JsonSerializerOptions { WriteIndented = true });
            }
            catch (Exception ex)
            {
                return $"Error retrieving invoice: {ex.Message}";
            }
        }

        [KernelFunction("get_invoices_by_customer")]
        [Description("Retrieves all invoices for a specific customer")]
        public async Task<string> GetInvoicesByCustomerAsync(
            [Description("Customer name to search for")] string customerName)
        {
            try
            {
                var invoices = await _invoiceRepository.GetByCustomerNameAsync(customerName);
                if (!invoices.Any())
                {
                    return $"No invoices found for customer '{customerName}'.";
                }

                return JsonSerializer.Serialize(invoices, new JsonSerializerOptions { WriteIndented = true });
            }
            catch (Exception ex)
            {
                return $"Error retrieving invoices: {ex.Message}";
            }
        }

        [KernelFunction("get_recent_invoices")]
        [Description("Retrieves the most recent invoices")]
        public async Task<string> GetRecentInvoicesAsync(
            [Description("Number of recent invoices to retrieve (default: 10)")] int count = 10)
        {
            try
            {
                var invoices = await _invoiceRepository.GetRecentInvoicesAsync(count);
                if (!invoices.Any())
                {
                    return "No invoices found.";
                }

                return JsonSerializer.Serialize(invoices, new JsonSerializerOptions { WriteIndented = true });
            }
            catch (Exception ex)
            {
                return $"Error retrieving recent invoices: {ex.Message}";
            }
        }
    }
}
