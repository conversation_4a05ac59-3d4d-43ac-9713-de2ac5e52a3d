﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
	<NoWarn>$(NoWarn);CS8618,IDE0009,CA1051,CA1050,CA1707,<PERSON>1054,CA2007,<PERSON><PERSON><PERSON>RD111,<PERSON>1591,<PERSON><PERSON>1110,<PERSON><PERSON>1243,<PERSON>5394,SKEXP0001,SKEX<PERSON>0010,SKEXP0020,SKEXP0040,SKEXP0050,<PERSON>EX<PERSON>0060,SKEXP0070,SKEXP0101,<PERSON>EXP0110,OPENAI001</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="BCrypt.Net-Core" Version="1.6.0" />
    <PackageReference Include="ClosedXML" Version="0.104.2" />
    <PackageReference Include="Dapper" Version="2.1.66" />
    <PackageReference Include="DocumentFormat.OpenXml" Version="3.3.0" />
    <PackageReference Include="Hangfire" Version="1.8.18" />
    <PackageReference Include="Hangfire.AspNetCore" Version="1.8.18" />
    <PackageReference Include="Hangfire.SqlServer" Version="1.8.18" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.1" />
    <PackageReference Include="Microsoft.KernelMemory" Version="0.98.250324.1" />
    <PackageReference Include="Microsoft.KernelMemory.Core" Version="0.98.250324.1" />
    <PackageReference Include="Microsoft.SemanticKernel" Version="1.44.0" />
    <PackageReference Include="Microsoft.SemanticKernel.Agents.Abstractions" Version="1.44.0-preview" />
    <PackageReference Include="Microsoft.SemanticKernel.Agents.Core" Version="1.44.0-preview" />
    <PackageReference Include="Microsoft.SemanticKernel.Connectors.Google" Version="1.44.0-alpha" />
    <PackageReference Include="Microsoft.SemanticKernel.Connectors.Ollama" Version="1.44.0-alpha" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.2" />
    <PackageReference Include="Microsoft.SemanticKernel.Plugins.OpenApi" Version="1.40.1" />
    <PackageReference Include="ModelContextProtocol" Version="0.1.0-preview.6" />
    <PackageReference Include="OpenTelemetry" Version="1.11.2" />
    <PackageReference Include="OpenTelemetry.Api" Version="1.11.2" />
    <PackageReference Include="OpenTelemetry.Exporter.Console" Version="1.11.2" />
    <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.11.2" />
    <PackageReference Include="PdfPig" Version="0.1.10" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ProjectApp.Core\ProjectApp.Core.csproj" />
  </ItemGroup>

</Project>
